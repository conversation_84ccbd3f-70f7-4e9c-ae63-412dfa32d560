2025-03-31 01:32:52,784 [INFO] Activator:176 - 


2025-03-31 01:32:52,786 [INFO] Activator:177 - !SESSION log4j initialized
2025-03-31 01:32:56,615 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 
2025-03-31 01:32:59,339 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554
2025-03-31 01:32:59,363 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/mcu/
2025-03-31 01:32:59,364 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/plugins/boardmanager/
2025-03-31 01:32:59,365 [WARN] ApiDb:259 - Overriding images path with different value:  => C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/plugins/mcufinder/images/
2025-03-31 01:32:59,377 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-03-31 01:32:59,378 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-03-31 01:32:59,382 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-03-31 01:32:59,523 [INFO] RulesReader:64 - Compatibility file has been processed (301 Rules)
2025-03-31 01:32:59,652 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/mcu/
2025-03-31 01:32:59,652 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/plugins/boardmanager/
2025-03-31 01:32:59,652 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/plugins/mcufinder/images/
2025-03-31 01:32:59,652 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-03-31 01:32:59,652 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-03-31 01:32:59,653 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-03-31 01:32:59,653 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-03-31 01:32:59,653 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-03-31 01:32:59,653 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-03-31 01:32:59,653 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-03-31 01:32:59,762 [INFO] MainPanel:272 - HeapMemory: 268435456
2025-03-31 01:32:59,943 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/mcu/
2025-03-31 01:32:59,943 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/plugins/boardmanager/
2025-03-31 01:32:59,943 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\\db\/plugins/mcufinder/images/
2025-03-31 01:32:59,944 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-03-31 01:32:59,944 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-03-31 01:32:59,944 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-03-31 01:32:59,944 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-03-31 01:32:59,944 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-03-31 01:32:59,944 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-03-31 01:32:59,944 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-03-31 01:32:59,974 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554
2025-03-31 01:32:59,976 [INFO] PluginManage:196 - Search for loadable plugins [exclusion list=, ]
2025-03-31 01:32:59,978 [INFO] PluginManage:310 - Check plugin analytics
2025-03-31 01:33:00,330 [INFO] AnalyticsPlugin:253 - Accepted Software Licenses: STM32CubeMX.6.14.0
2025-03-31 01:33:00,331 [INFO] AnalyticsPlugin:255 - Accepted CMSIS Pack Licenses: 
2025-03-31 01:33:00,331 [INFO] AnalyticsPlugin:257 - Accepted Firmware Licenses: FW.F4.1.28.0
2025-03-31 01:33:00,339 [INFO] PluginManage:359 - Loaded plugin analytics (category:tool,tabindex:-1)
2025-03-31 01:33:00,339 [INFO] PluginManage:310 - Check plugin cadmodel
2025-03-31 01:33:00,350 [INFO] CADModel:105 - Init CAD model plugin
2025-03-31 01:33:00,351 [INFO] PluginManage:359 - Loaded plugin cadmodel (category:power,tabindex:5)
2025-03-31 01:33:00,351 [INFO] PluginManage:310 - Check plugin clock
2025-03-31 01:33:00,379 [INFO] PluginManage:359 - Loaded plugin clock (category:base,tabindex:2)
2025-03-31 01:33:00,379 [INFO] PluginManage:310 - Check plugin ddr
2025-03-31 01:33:00,384 [INFO] PluginManage:359 - Loaded plugin ddr (category:tool,tabindex:6)
2025-03-31 01:33:00,385 [INFO] PluginManage:310 - Check plugin filemanager
2025-03-31 01:33:00,569 [INFO] PluginManage:359 - Loaded plugin filemanager (category:base,tabindex:10)
2025-03-31 01:33:00,569 [INFO] PluginManage:310 - Check plugin ipmanager
2025-03-31 01:33:00,579 [INFO] PluginManage:359 - Loaded plugin ipmanager (category:base,tabindex:5)
2025-03-31 01:33:00,579 [INFO] PluginManage:310 - Check plugin lpbam
2025-03-31 01:33:00,590 [INFO] PluginManage:359 - Loaded plugin lpbam (category:base,tabindex:0)
2025-03-31 01:33:00,590 [INFO] PluginManage:310 - Check plugin memorymap
2025-03-31 01:33:00,610 [INFO] PluginManage:359 - Loaded plugin memorymap (category:base,tabindex:4)
2025-03-31 01:33:00,610 [INFO] PluginManage:310 - Check plugin pinoutandconfiguration
2025-03-31 01:33:00,622 [INFO] PluginManage:359 - Loaded plugin pinoutandconfiguration (category:base,tabindex:1)
2025-03-31 01:33:00,622 [INFO] PluginManage:310 - Check plugin pinoutconfig
2025-03-31 01:33:00,746 [WARN] SupportedApi:132 - Cannot load RTOS API schema: s4s-elt-must-match.1: The content of 'definitions' must match (annotation?, (simpleType | complexType)?, (unique | key | keyref)*)). A problem was found starting at: attribute.
2025-03-31 01:33:00,920 [INFO] PluginManage:359 - Loaded plugin pinoutconfig (category:base,tabindex:0)
2025-03-31 01:33:00,920 [INFO] PluginManage:310 - Check plugin power
2025-03-31 01:33:00,934 [INFO] PluginManage:359 - Loaded plugin power (category:power,tabindex:4)
2025-03-31 01:33:00,934 [INFO] PluginManage:310 - Check plugin projectmanager
2025-03-31 01:33:00,954 [INFO] PluginManage:359 - Loaded plugin projectmanager (category:projectmanager,tabindex:4)
2025-03-31 01:33:00,954 [INFO] PluginManage:310 - Check plugin rif
2025-03-31 01:33:00,964 [INFO] PluginManage:359 - Loaded plugin rif (category:base,tabindex:3)
2025-03-31 01:33:00,965 [INFO] PluginManage:310 - Check plugin thirdparty
2025-03-31 01:33:01,177 [INFO] PluginManage:359 - Loaded plugin thirdparty (category:base,tabindex:-1)
2025-03-31 01:33:01,175 [WARN] IntegrityCheckThread:84 - waiting for thirdparty lock release [integrity check]
2025-03-31 01:33:01,177 [INFO] PluginManage:310 - Check plugin tools
2025-03-31 01:33:01,177 [INFO] IntegrityCheckThread:86 - entering critical section [integrity check]
2025-03-31 01:33:01,178 [INFO] ThirdPartyUpdaterWithRetryManager:70 - Updater plugin not ready yet. [1/15]
2025-03-31 01:33:01,183 [INFO] PluginManage:359 - Loaded plugin tools (category:base,tabindex:7)
2025-03-31 01:33:01,183 [INFO] PluginManage:310 - Check plugin tutovideos
2025-03-31 01:33:01,407 [INFO] PluginManage:359 - Loaded plugin tutovideos (category:base,tabindex:-1)
2025-03-31 01:33:01,407 [INFO] PluginManage:310 - Check plugin updater
2025-03-31 01:33:01,435 [INFO] PluginManage:359 - Loaded plugin updater (category:base,tabindex:12)
2025-03-31 01:33:01,435 [INFO] PluginManage:310 - Check plugin userauth
2025-03-31 01:33:01,441 [INFO] UserAuth:118 - Init User Auth plugin
2025-03-31 01:33:01,442 [INFO] PluginManage:359 - Loaded plugin userauth (category:base,tabindex:14)
2025-03-31 01:33:01,442 [INFO] PluginManage:283 - PluginManage : Loaded plugins [18]
2025-03-31 01:33:01,713 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-03-31 01:33:01,826 [INFO] CADModel:165 - CPN selected for project level
2025-03-31 01:33:01,827 [INFO] CADModel:114 - Register for checkConnection events
2025-03-31 01:33:01,852 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,852 [INFO] PluginManager:220 - loadIPPluginJar : add adc
2025-03-31 01:33:01,857 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,857 [INFO] PluginManager:220 - loadIPPluginJar : add aes
2025-03-31 01:33:01,861 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,862 [INFO] PluginManager:220 - loadIPPluginJar : add can
2025-03-31 01:33:01,864 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,864 [INFO] PluginManager:220 - loadIPPluginJar : add comp
2025-03-31 01:33:01,867 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,868 [INFO] PluginManager:220 - loadIPPluginJar : add cryp
2025-03-31 01:33:01,872 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,872 [INFO] PluginManager:220 - loadIPPluginJar : add ddr_ctrl_phy
2025-03-31 01:33:01,875 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,875 [INFO] PluginManager:220 - loadIPPluginJar : add dfsdm
2025-03-31 01:33:01,882 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,882 [INFO] PluginManager:220 - loadIPPluginJar : add dma
2025-03-31 01:33:01,887 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,887 [INFO] PluginManager:220 - loadIPPluginJar : add dma3
2025-03-31 01:33:01,891 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,891 [INFO] PluginManager:220 - loadIPPluginJar : add extmemmanager
2025-03-31 01:33:01,893 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,893 [INFO] PluginManager:220 - loadIPPluginJar : add fatfs
2025-03-31 01:33:01,899 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,899 [INFO] PluginManager:220 - loadIPPluginJar : add fmc
2025-03-31 01:33:01,909 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,909 [INFO] PluginManager:220 - loadIPPluginJar : add freertos
2025-03-31 01:33:01,911 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,912 [INFO] PluginManager:220 - loadIPPluginJar : add genericplugin
2025-03-31 01:33:01,915 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,916 [INFO] PluginManager:220 - loadIPPluginJar : add gfxmmu
2025-03-31 01:33:01,923 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,923 [INFO] PluginManager:220 - loadIPPluginJar : add gic
2025-03-31 01:33:01,929 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,929 [INFO] PluginManager:220 - loadIPPluginJar : add gpio
2025-03-31 01:33:01,934 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,934 [INFO] PluginManager:220 - loadIPPluginJar : add gtzc
2025-03-31 01:33:01,938 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,938 [INFO] PluginManager:220 - loadIPPluginJar : add hash
2025-03-31 01:33:01,942 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,942 [INFO] PluginManager:220 - loadIPPluginJar : add i2c
2025-03-31 01:33:01,947 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,947 [INFO] PluginManager:220 - loadIPPluginJar : add i2s
2025-03-31 01:33:01,953 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,954 [INFO] PluginManager:220 - loadIPPluginJar : add i3c
2025-03-31 01:33:01,959 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,959 [INFO] PluginManager:220 - loadIPPluginJar : add ipddr
2025-03-31 01:33:01,971 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,972 [INFO] PluginManager:220 - loadIPPluginJar : add linkedlist
2025-03-31 01:33:01,979 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,980 [INFO] PluginManager:220 - loadIPPluginJar : add lorawan
2025-03-31 01:33:01,982 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,982 [INFO] PluginManager:220 - loadIPPluginJar : add ltdc
2025-03-31 01:33:01,990 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,990 [INFO] PluginManager:220 - loadIPPluginJar : add mdma
2025-03-31 01:33:01,996 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:01,997 [INFO] PluginManager:220 - loadIPPluginJar : add nvic
2025-03-31 01:33:02,001 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,001 [INFO] PluginManager:220 - loadIPPluginJar : add opamp
2025-03-31 01:33:02,005 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,005 [INFO] PluginManager:220 - loadIPPluginJar : add openamp
2025-03-31 01:33:02,010 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,010 [INFO] PluginManager:220 - loadIPPluginJar : add pdm2pcm
2025-03-31 01:33:02,020 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,022 [INFO] PluginManager:220 - loadIPPluginJar : add plateformsettings
2025-03-31 01:33:02,025 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,026 [INFO] PluginManager:220 - loadIPPluginJar : add quadspi
2025-03-31 01:33:02,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,030 [INFO] PluginManager:220 - loadIPPluginJar : add radio
2025-03-31 01:33:02,037 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,037 [INFO] PluginManager:220 - loadIPPluginJar : add resmgrutility
2025-03-31 01:33:02,042 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,043 [INFO] PluginManager:220 - loadIPPluginJar : add sai
2025-03-31 01:33:02,047 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,047 [INFO] PluginManager:220 - loadIPPluginJar : add spi
2025-03-31 01:33:02,054 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,055 [INFO] PluginManager:220 - loadIPPluginJar : add stm32_wpan
2025-03-31 01:33:02,058 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,058 [INFO] PluginManager:220 - loadIPPluginJar : add tim
2025-03-31 01:33:02,064 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,064 [INFO] PluginManager:220 - loadIPPluginJar : add touchsensing
2025-03-31 01:33:02,067 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,068 [INFO] PluginManager:220 - loadIPPluginJar : add tracer_emb
2025-03-31 01:33:02,070 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,070 [INFO] PluginManager:220 - loadIPPluginJar : add ts
2025-03-31 01:33:02,073 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,073 [INFO] PluginManager:220 - loadIPPluginJar : add tsc
2025-03-31 01:33:02,076 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,076 [INFO] PluginManager:220 - loadIPPluginJar : add ucpd
2025-03-31 01:33:02,080 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,080 [INFO] PluginManager:220 - loadIPPluginJar : add usart
2025-03-31 01:33:02,085 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-03-31 01:33:02,085 [INFO] PluginManager:220 - loadIPPluginJar : add usbx
2025-03-31 01:33:02,228 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:02,264 [INFO] RulesReader:64 - Compatibility file has been processed (301 Rules)
2025-03-31 01:33:02,280 [INFO] RulesReader:64 - Compatibility file has been processed (301 Rules)
2025-03-31 01:33:02,291 [INFO] CADModel:165 - CPN selected for project level
2025-03-31 01:33:02,291 [INFO] CADModel:114 - Register for checkConnection events
2025-03-31 01:33:02,291 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:02,292 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-03-31 01:33:02,500 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:02,502 [INFO] CADModel:165 - CPN selected for project level
2025-03-31 01:33:02,503 [INFO] CADModel:114 - Register for checkConnection events
2025-03-31 01:33:02,503 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:02,503 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-03-31 01:33:02,506 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:02,664 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:02,672 [INFO] DbMcusAds:53 - JSON generation date=Thu Mar 06 20:24:21 IST 2025 (1741272861825)
2025-03-31 01:33:02,673 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:02,722 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-03-31 01:33:03,044 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:03,046 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:03,046 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:03,047 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-03-31 01:33:03,048 [FATAL] Updater:351 - Updater called before beeing initialized
2025-03-31 01:33:03,112 [ERROR] Updater:1198 - MainUpdater not yet initialized. External WinMGr cannot be set.
2025-03-31 01:33:03,115 [INFO] Updater:1134 - Updater Version found : 6.14.0
2025-03-31 01:33:03,138 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554
2025-03-31 01:33:04,178 [INFO] ThirdPartyUpdaterWithRetryManager:70 - Updater plugin not ready yet. [2/15]
2025-03-31 01:33:04,711 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] Mar 31, 2025 1:33:04 AM java.util.prefs.WindowsPreferences <init>
2025-03-31 01:33:04,714 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] WARNING: Could not open/create prefs root node Software\JavaSoft\Prefs at root 0xffffffff80000002. Windows RegCreateKeyEx(...) returned error code 5.
2025-03-31 01:33:04,714 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] 
2025-03-31 01:33:05,304 [INFO] MainUpdater:2872 - connection check result : 10
2025-03-31 01:33:05,305 [INFO] MainUpdater:289 - Updater Check For Update Now.
2025-03-31 01:33:05,306 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.140
2025-03-31 01:33:05,323 [INFO] McuFinderGlobals:63 - Set McuFinder mode to 2 (CubeIDE integrated)
2025-03-31 01:33:05,325 [INFO] UserAuth:486 - Internet connection configuration mode: 1
2025-03-31 01:33:05,403 [INFO] JxBrowserEngine:152 - Initiate JxBrowser Engine with user profile folder
2025-03-31 01:33:05,552 [INFO] CheckServerUpdateThread:120 - End of CheckServer Thread
2025-03-31 01:33:06,644 [INFO] WebApp:169 - Instantiating new browser for Auth
2025-03-31 01:33:07,604 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENVWB1.1.4.0
2025-03-31 01:33:07,614 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-ASTRA1.2.0.2
2025-03-31 01:33:07,625 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SMBUS.2.1.0
2025-03-31 01:33:07,636 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-WB05N.1.1.0
2025-03-31 01:33:07,669 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F7.1.1.0
2025-03-31 01:33:07,704 [WARN] PackLoader:240 - Cannot read IP mode file for Infineon.AIROC-Wi-Fi-Bluetooth-STM32.1.7.0
2025-03-31 01:33:07,726 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DISPLAY.3.0.0
2025-03-31 01:33:07,738 [INFO] WebApp:463 - Apply proxy settings
2025-03-31 01:33:07,738 [INFO] WebApp:548 - Chromium requires no authentication
2025-03-31 01:33:07,740 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC10.1.0.0
2025-03-31 01:33:07,752 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLEMGR.4.0.0
2025-03-31 01:33:07,762 [WARN] PackLoader:240 - Cannot read IP mode file for emotas.I-CUBE-CANOPEN.1.3.0
2025-03-31 01:33:07,769 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : Cortex-A Device cause : null
2025-03-31 01:33:07,790 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-03-31 01:33:07,790 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-03-31 01:33:07,790 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-03-31 01:33:07,798 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AI.10.0.0
2025-03-31 01:33:07,806 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-SMARTAG2.1.2.0
2025-03-31 01:33:07,807 [INFO] WebApp:491 - Direct internet connection detected
2025-03-31 01:33:07,812 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-FLIGHT1.5.1.0
2025-03-31 01:33:07,860 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-MEMS1.11.1.0
2025-03-31 01:33:08,036 [INFO] WebApp:894 - Register for checkConnection events
2025-03-31 01:33:08,036 [INFO] WebApp:463 - Apply proxy settings
2025-03-31 01:33:08,036 [INFO] WebApp:548 - Chromium requires no authentication
2025-03-31 01:33:08,037 [INFO] WebApp:491 - Direct internet connection detected
2025-03-31 01:33:08,056 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-03-31 01:33:08,058 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-03-31 01:33:08,060 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-03-31 01:33:08,061 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-03-31 01:33:08,063 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-03-31 01:33:08,066 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-WL.2.0.0
2025-03-31 01:33:08,073 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENV1.5.0.0
2025-03-31 01:33:08,087 [WARN] PackLoader:240 - Cannot read IP mode file for WES.I-CUBE-Cesium.1.3.0
2025-03-31 01:33:08,093 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE2.3.3.0
2025-03-31 01:33:08,100 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC9.1.0.0
2025-03-31 01:33:08,109 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE1.7.1.0
2025-03-31 01:33:08,117 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfMQTT.1.19.2
2025-03-31 01:33:08,131 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-G0.1.1.0
2025-03-31 01:33:08,139 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SAFEA1.1.2.2
2025-03-31 01:33:08,147 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC4.3.0.0
2025-03-31 01:33:08,158 [WARN] PackLoader:240 - Cannot read IP mode file for EmbeddedOffice.I-CUBE-FS-RTOS.1.0.1
2025-03-31 01:33:08,165 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfTPM.3.8.0
2025-03-31 01:33:08,171 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TCPP.4.2.0
2025-03-31 01:33:08,180 [WARN] PackLoader:240 - Cannot read IP mode file for RealThread.X-CUBE-RT-Thread_Nano.4.1.1
2025-03-31 01:33:08,186 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-SIGFOX1.3.2.0
2025-03-31 01:33:08,192 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-EEPRMA1.5.1.0
2025-03-31 01:33:08,199 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfSSL.5.7.6
2025-03-31 01:33:08,207 [WARN] PackLoader:240 - Cannot read IP mode file for ITTIA_DB.I-CUBE-ITTIADB.8.9.0
2025-03-31 01:33:08,232 [WARN] PackLoader:240 - Cannot read IP mode file for SEGGER.I-CUBE-embOS.1.3.1
2025-03-31 01:33:08,328 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0
2025-03-31 01:33:08,356 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F4.1.1.0
2025-03-31 01:33:08,365 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ISPU.2.1.0
2025-03-31 01:33:08,382 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-L5.2.0.0
2025-03-31 01:33:08,383 [INFO] WebApp:191 - Connection restablished
2025-03-31 01:33:08,392 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC6.3.1.0
2025-03-31 01:33:08,399 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DPower.1.2.0
2025-03-31 01:33:08,406 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-FREERTOS.1.3.0
2025-03-31 01:33:08,411 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC7.1.0.1
2025-03-31 01:33:08,417 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DPower.1.3.0
2025-03-31 01:33:08,432 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-03-31 01:33:08,433 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-L4.2.0.0
2025-03-31 01:33:08,434 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-03-31 01:33:08,434 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-03-31 01:33:08,435 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-03-31 01:33:08,443 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SFXS2LP1.4.0.0
2025-03-31 01:33:08,469 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7.3.3.0
2025-03-31 01:33:08,484 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-03-31 01:33:08,485 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-03-31 01:33:08,487 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-WB.2.0.0
2025-03-31 01:33:08,488 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-03-31 01:33:08,488 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-03-31 01:33:08,488 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-03-31 01:33:08,489 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-03-31 01:33:08,489 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-03-31 01:33:08,498 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-GNSS1.7.0.1
2025-03-31 01:33:08,509 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0
2025-03-31 01:33:08,517 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-STBOX1.2.0.0
2025-03-31 01:33:08,534 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SUBG2.5.0.0
2025-03-31 01:33:08,554 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7RS.1.1.0
2025-03-31 01:33:08,565 [WARN] PackLoader:240 - Cannot read IP mode file for Cesanta.I-CUBE-Mongoose.7.13.0
2025-03-31 01:33:08,578 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-G4.2.0.0
2025-03-31 01:33:08,585 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfSSH.1.4.20
2025-03-31 01:33:08,611 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-IPS.3.1.0
2025-03-31 01:33:08,624 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ALS.1.0.2
2025-03-31 01:33:08,633 [WARN] PackLoader:240 - Cannot read IP mode file for portGmbH.I-Cube-SoM-uGOAL.1.1.0
2025-03-31 01:33:08,647 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOF1.3.4.2
2025-03-31 01:33:08,658 [INFO] ThirdParty:978 - Integrity check success = true
2025-03-31 01:33:08,658 [INFO] IntegrityCheckThread:100 - exiting critical section [integrity check]
2025-03-31 01:33:08,658 [INFO] IntegrityCheckThread:103 - End integrity checks thread
2025-03-31 01:33:09,168 [INFO] WebApp:225 - Starting web application
2025-03-31 01:33:09,168 [INFO] WebApp:593 - Web application path used C:\ST\STM32CubeIDE_1.18.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.0.202502271554\db\plugins\mcufinder\reactClient1\index.html
2025-03-31 01:33:09,265 [INFO] UserAuth:486 - Internet connection configuration mode: 1
2025-03-31 01:33:18,461 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] 
