[PreviousLibFiles]
LibFiles=Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_bus.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_rcc.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_system.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_utils.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_gpio.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_dma.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_dmamux.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_pwr.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_cortex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h;Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_exti.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_i2c.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2s.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2s_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_iwdg.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_iwdg.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_spi.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usart.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_wwdg.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_wwdg.h;Middlewares\Third_Party\FreeRTOS\Source\include\croutine.h;Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h;Middlewares\Third_Party\FreeRTOS\Source\include\event_groups.h;Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h;Middlewares\Third_Party\FreeRTOS\Source\include\list.h;Middlewares\Third_Party\FreeRTOS\Source\include\message_buffer.h;Middlewares\Third_Party\FreeRTOS\Source\include\mpu_prototypes.h;Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h;Middlewares\Third_Party\FreeRTOS\Source\include\portable.h;Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h;Middlewares\Third_Party\FreeRTOS\Source\include\queue.h;Middlewares\Third_Party\FreeRTOS\Source\include\semphr.h;Middlewares\Third_Party\FreeRTOS\Source\include\stack_macros.h;Middlewares\Third_Party\FreeRTOS\Source\include\StackMacros.h;Middlewares\Third_Party\FreeRTOS\Source\include\stream_buffer.h;Middlewares\Third_Party\FreeRTOS\Source\include\task.h;Middlewares\Third_Party\FreeRTOS\Source\include\timers.h;Middlewares\Third_Party\FreeRTOS\Source\include\atomic.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\freertos_mpool.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\freertos_os2.h;Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F\portmacro.h;Middlewares\Third_Party\FatFs\src\diskio.h;Middlewares\Third_Party\FatFs\src\ff.h;Middlewares\Third_Party\FatFs\src\ff_gen_drv.h;Middlewares\Third_Party\FatFs\src\integer.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h;Middlewares\ST\STM32_USB_Host_Library\Class\CDC\Inc\usbh_cdc.h;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_hcd.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_usb.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2s.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2s_ex.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_iwdg.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c;Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_wwdg.c;Middlewares\Third_Party\FreeRTOS\Source\croutine.c;Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;Middlewares\Third_Party\FreeRTOS\Source\list.c;Middlewares\Third_Party\FreeRTOS\Source\queue.c;Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;Middlewares\Third_Party\FreeRTOS\Source\tasks.c;Middlewares\Third_Party\FreeRTOS\Source\timers.c;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F\port.c;Middlewares\Third_Party\FatFs\src\diskio.c;Middlewares\Third_Party\FatFs\src\ff.c;Middlewares\Third_Party\FatFs\src\ff_gen_drv.c;Middlewares\Third_Party\FatFs\src\option\syscall.c;Middlewares\Third_Party\FatFs\src\option\ccsbcs.c;Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_core.c;Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_ctlreq.c;Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_ioreq.c;Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_pipes.c;Middlewares\ST\STM32_USB_Host_Library\Class\CDC\Src\usbh_cdc.c;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_bus.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_rcc.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_system.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_utils.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_gpio.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_dma.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_dmamux.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_pwr.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_cortex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h;Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_exti.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_i2c.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2s.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2s_ex.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_iwdg.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_iwdg.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_spi.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usart.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_wwdg.h;Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_wwdg.h;Middlewares\Third_Party\FreeRTOS\Source\include\croutine.h;Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h;Middlewares\Third_Party\FreeRTOS\Source\include\event_groups.h;Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h;Middlewares\Third_Party\FreeRTOS\Source\include\list.h;Middlewares\Third_Party\FreeRTOS\Source\include\message_buffer.h;Middlewares\Third_Party\FreeRTOS\Source\include\mpu_prototypes.h;Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h;Middlewares\Third_Party\FreeRTOS\Source\include\portable.h;Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h;Middlewares\Third_Party\FreeRTOS\Source\include\queue.h;Middlewares\Third_Party\FreeRTOS\Source\include\semphr.h;Middlewares\Third_Party\FreeRTOS\Source\include\stack_macros.h;Middlewares\Third_Party\FreeRTOS\Source\include\StackMacros.h;Middlewares\Third_Party\FreeRTOS\Source\include\stream_buffer.h;Middlewares\Third_Party\FreeRTOS\Source\include\task.h;Middlewares\Third_Party\FreeRTOS\Source\include\timers.h;Middlewares\Third_Party\FreeRTOS\Source\include\atomic.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\freertos_mpool.h;Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\freertos_os2.h;Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F\portmacro.h;Middlewares\Third_Party\FatFs\src\diskio.h;Middlewares\Third_Party\FatFs\src\ff.h;Middlewares\Third_Party\FatFs\src\ff_gen_drv.h;Middlewares\Third_Party\FatFs\src\integer.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h;Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h;Middlewares\ST\STM32_USB_Host_Library\Class\CDC\Inc\usbh_cdc.h;Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h;Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h;Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h;Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h;Drivers\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c;Drivers\CMSIS\Include\cachel1_armv7.h;Drivers\CMSIS\Include\cmsis_armcc.h;Drivers\CMSIS\Include\cmsis_armclang.h;Drivers\CMSIS\Include\cmsis_armclang_ltm.h;Drivers\CMSIS\Include\cmsis_compiler.h;Drivers\CMSIS\Include\cmsis_gcc.h;Drivers\CMSIS\Include\cmsis_iccarm.h;Drivers\CMSIS\Include\cmsis_version.h;Drivers\CMSIS\Include\core_armv81mml.h;Drivers\CMSIS\Include\core_armv8mbl.h;Drivers\CMSIS\Include\core_armv8mml.h;Drivers\CMSIS\Include\core_cm0.h;Drivers\CMSIS\Include\core_cm0plus.h;Drivers\CMSIS\Include\core_cm1.h;Drivers\CMSIS\Include\core_cm23.h;Drivers\CMSIS\Include\core_cm3.h;Drivers\CMSIS\Include\core_cm33.h;Drivers\CMSIS\Include\core_cm35p.h;Drivers\CMSIS\Include\core_cm4.h;Drivers\CMSIS\Include\core_cm55.h;Drivers\CMSIS\Include\core_cm7.h;Drivers\CMSIS\Include\core_cm85.h;Drivers\CMSIS\Include\core_sc000.h;Drivers\CMSIS\Include\core_sc300.h;Drivers\CMSIS\Include\core_starmc1.h;Drivers\CMSIS\Include\mpu_armv7.h;Drivers\CMSIS\Include\mpu_armv8.h;Drivers\CMSIS\Include\pac_armv81.h;Drivers\CMSIS\Include\pmu_armv8.h;Drivers\CMSIS\Include\tz_context.h;

[PreviousUsedCMakes]
SourceFiles=Core\Src\main.c;Core\Src\gpio.c;Core\Src\freertos.c;FATFS\Target\user_diskio.c;FATFS\App\fatfs.c;Core\Src\i2c.c;Core\Src\i2s.c;Core\Src\iwdg.c;Core\Src\spi.c;Core\Src\usart.c;USB_HOST\App\usb_host.c;USB_HOST\Target\usbh_conf.c;USB_HOST\Target\usbh_platform.c;Core\Src\wwdg.c;Core\Src\stm32f4xx_it.c;Core\Src\stm32f4xx_hal_msp.c;Core\Src\stm32f4xx_hal_timebase_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_hcd.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_usb.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2s.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2s_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_iwdg.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_wwdg.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F\port.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\diskio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\ff.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\ff_gen_drv.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\option\syscall.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\option\ccsbcs.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_core.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_ctlreq.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_ioreq.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_pipes.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Class\CDC\Src\usbh_cdc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c;Core\Src\system_stm32f4xx.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_hcd.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_usb.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2s.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2s_ex.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_iwdg.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_wwdg.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F\port.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\diskio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\ff.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\ff_gen_drv.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\option\syscall.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\option\ccsbcs.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_core.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_ctlreq.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_ioreq.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_pipes.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Class\CDC\Src\usbh_cdc.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c;Core\Src\system_stm32f4xx.c;;;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\diskio.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\ff.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\ff_gen_drv.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\option\syscall.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src\option\ccsbcs.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\croutine.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\list.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\queue.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\tasks.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\timers.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F\port.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_core.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_ctlreq.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_ioreq.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Src\usbh_pipes.c;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Class\CDC\Src\usbh_cdc.c;
HeaderPath=C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Inc;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\include;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\Third_Party\FatFs\src;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Core\Inc;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Middlewares\ST\STM32_USB_Host_Library\Class\CDC\Inc;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\CMSIS\Device\ST\STM32F4xx\Include;C:\Users\<USER>\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.2\Drivers\CMSIS\Include;Core\Inc;FATFS\Target;FATFS\App;USB_HOST\App;USB_HOST\Target;
CDefines=USE_HAL_DRIVER;STM32F407xx;USE_HAL_DRIVER;USE_HAL_DRIVER;

[PreviousGenFiles]
AdvancedFolderStructure=true
HeaderFileListSize=17
HeaderFiles#0=..\Core\Inc\gpio.h
HeaderFiles#1=..\Core\Inc\FreeRTOSConfig.h
HeaderFiles#2=..\FATFS\Target\ffconf.h
HeaderFiles#3=..\FATFS\Target\user_diskio.h
HeaderFiles#4=..\FATFS\App\fatfs.h
HeaderFiles#5=..\Core\Inc\i2c.h
HeaderFiles#6=..\Core\Inc\i2s.h
HeaderFiles#7=..\Core\Inc\iwdg.h
HeaderFiles#8=..\Core\Inc\spi.h
HeaderFiles#9=..\Core\Inc\usart.h
HeaderFiles#10=..\USB_HOST\App\usb_host.h
HeaderFiles#11=..\USB_HOST\Target\usbh_conf.h
HeaderFiles#12=..\USB_HOST\Target\usbh_platform.h
HeaderFiles#13=..\Core\Inc\wwdg.h
HeaderFiles#14=..\Core\Inc\stm32f4xx_it.h
HeaderFiles#15=..\Core\Inc\stm32f4xx_hal_conf.h
HeaderFiles#16=..\Core\Inc\main.h
HeaderFolderListSize=5
HeaderPath#0=..\Core\Inc
HeaderPath#1=..\FATFS\Target
HeaderPath#2=..\FATFS\App
HeaderPath#3=..\USB_HOST\App
HeaderPath#4=..\USB_HOST\Target
HeaderFiles=;
SourceFileListSize=17
SourceFiles#0=..\Core\Src\gpio.c
SourceFiles#1=..\Core\Src\freertos.c
SourceFiles#2=..\FATFS\Target\user_diskio.c
SourceFiles#3=..\FATFS\App\fatfs.c
SourceFiles#4=..\Core\Src\i2c.c
SourceFiles#5=..\Core\Src\i2s.c
SourceFiles#6=..\Core\Src\iwdg.c
SourceFiles#7=..\Core\Src\spi.c
SourceFiles#8=..\Core\Src\usart.c
SourceFiles#9=..\USB_HOST\App\usb_host.c
SourceFiles#10=..\USB_HOST\Target\usbh_conf.c
SourceFiles#11=..\USB_HOST\Target\usbh_platform.c
SourceFiles#12=..\Core\Src\wwdg.c
SourceFiles#13=..\Core\Src\stm32f4xx_it.c
SourceFiles#14=..\Core\Src\stm32f4xx_hal_msp.c
SourceFiles#15=..\Core\Src\stm32f4xx_hal_timebase_tim.c
SourceFiles#16=..\Core\Src\main.c
SourceFolderListSize=5
SourcePath#0=..\Core\Src
SourcePath#1=..\FATFS\Target
SourcePath#2=..\FATFS\App
SourcePath#3=..\USB_HOST\App
SourcePath#4=..\USB_HOST\Target
SourceFiles=;

