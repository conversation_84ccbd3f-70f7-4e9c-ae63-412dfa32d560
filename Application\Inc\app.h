#ifndef __APP_H
#define __APP_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "FreeRTOS.h"
#include "task.h"

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported macro ------------------------------------------------------------*/
/* Exported functions prototypes ---------------------------------------------*/
void APP_Init(void);
void APP_Start(void);

/* Task Handles */
extern TaskHandle_t xLedTaskHandle;

/* Task Functions */
void LED_Task(void *argument);

#ifdef __cplusplus
}
#endif

#endif /* __APP_H */ 