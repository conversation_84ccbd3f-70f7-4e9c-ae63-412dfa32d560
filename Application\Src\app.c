/* Includes ------------------------------------------------------------------*/
#include "app.h"

/* Private includes ----------------------------------------------------------*/
/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
#define LED_TASK_STACK_SIZE 128

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
TaskHandle_t xLedTaskHandle = NULL;

/* Private function prototypes -----------------------------------------------*/
/* Private functions --------------------------------------------------------*/

/**
  * @brief  LED Task implementation
  * @param  argument: Not used
  * @retval None
  */
void LED_Task(void *argument)
{
    argument = argument;

    /* Infinite loop */
    for(;;)
    {
        HAL_GPIO_TogglePin(LD4_GPIO_Port, LD4_Pin);
        vTaskDelay(pdMS_TO_TICKS(500));
    }
}

/**
  * @brief  Initialize the application
  * @retval None
  */
void APP_Init(void)
{
    /* Create the tasks */
    xTaskCreate(LED_Task, "LED", LED_TASK_STACK_SIZE, NULL, 1, &xLedTaskHandle);
}

/**
  * @brief  Start the application
  * @retval None
  */
void APP_Start(void)
{
    /* Start the scheduler */
    vTaskStartScheduler();
}
