#MicroXplorer Configuration settings - do not modify
CAD.formats=[{"id"\:42,"cad_product"\:"<PERSON>CA<PERSON> v6+","cad_family"\:"KiCAD"}]
CAD.pinconfig=Dual
CAD.provider=
FATFS.IPParameters=_USE_FIND,_CODE_PAGE,_USE_MUTEX,_USE_LFN,_FS_RPATH
FATFS._CODE_PAGE=850
FATFS._FS_RPATH=2
FATFS._USE_FIND=1
FATFS._USE_LFN=3
FATFS._USE_MUTEX=1
FREERTOS.BinarySemaphores01=myBinarySem01,Dynamic,NULL,Available
FREERTOS.CountingSemaphores01=myCountingSem01,2,Dynamic,NULL,0
FREERTOS.Events01=myEvent01,Dynamic,NULL
FREERTOS.FootprintOK=true
FREERTOS.IPParameters=Tasks01,configUSE_IDLE_HOOK,configUSE_CO_ROUTINES,configUSE_POSIX_ERRNO,Timers01,BinarySemaphores01,CountingSemaphores01,Mutexes01,RecursiveMutexes01,Events01,Queues01,FootprintOK
FREERTOS.Mutexes01=myMutex01,Dynamic,NULL,Available
FREERTOS.Queues01=myRxQueue01,16,uint16_t,0,Dynamic,NULL,NULL;myTxQueue01,16,uint16_t,0,Dynamic,NULL,NULL
FREERTOS.RecursiveMutexes01=myRecursiveMutex01,Dynamic,NULL,Available
FREERTOS.Tasks01=idleTask,24,128,StartIdleTask,Default,NULL,Dynamic,NULL,NULL;SerialTxTask01,8,128,StartSerialTXTask,Default,NULL,Dynamic,NULL,NULL;SerialRxTask01,8,128,StartSerialRXTask,Default,NULL,Dynamic,NULL,NULL
FREERTOS.Timers01=myTimer01,Callback01,osTimerPeriodic,Default,NULL,Dynamic,NULL
FREERTOS.configUSE_CO_ROUTINES=1
FREERTOS.configUSE_IDLE_HOOK=1
FREERTOS.configUSE_POSIX_ERRNO=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2S3.AudioFreq-Half_Duplex_Master=I2S_AUDIOFREQ_96K
I2S3.ErrorAudioFreq=-2.34 %
I2S3.ErrorAudioFreq-Half_Duplex_Master=0.8 %
I2S3.FullDuplexMode=I2S_FULLDUPLEXMODE_DISABLE
I2S3.IPParameters=AudioFreq-Half_Duplex_Master,ErrorAudioFreq-Half_Duplex_Master,RealAudioFreq-Half_Duplex_Master,RealAudioFreq,ErrorAudioFreq,Instance,VirtualMode,FullDuplexMode
I2S3.Instance=SPI$Index
I2S3.RealAudioFreq=93.75 KHz
I2S3.RealAudioFreq-Half_Duplex_Master=96.774 KHz
I2S3.VirtualMode=I2S_MODE_MASTER
KeepUserPlacement=false
Mcu.CPN=STM32F407VGT6
Mcu.Family=STM32F4
Mcu.IP0=FATFS
Mcu.IP1=FREERTOS
Mcu.IP10=USB_HOST
Mcu.IP11=USB_OTG_FS
Mcu.IP12=WWDG
Mcu.IP2=I2C1
Mcu.IP3=I2S3
Mcu.IP4=IWDG
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=SPI1
Mcu.IP8=SYS
Mcu.IP9=USART2
Mcu.IPNb=13
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE3
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin10=PA4
Mcu.Pin11=PA5
Mcu.Pin12=PA6
Mcu.Pin13=PA7
Mcu.Pin14=PB2
Mcu.Pin15=PB10
Mcu.Pin16=PD12
Mcu.Pin17=PD13
Mcu.Pin18=PD14
Mcu.Pin19=PD15
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin20=PC7
Mcu.Pin21=PA9
Mcu.Pin22=PA10
Mcu.Pin23=PA11
Mcu.Pin24=PA12
Mcu.Pin25=PA13
Mcu.Pin26=PA14
Mcu.Pin27=PC10
Mcu.Pin28=PC12
Mcu.Pin29=PD4
Mcu.Pin3=PH0-OSC_IN
Mcu.Pin30=PD5
Mcu.Pin31=PB3
Mcu.Pin32=PB6
Mcu.Pin33=PB9
Mcu.Pin34=PE1
Mcu.Pin35=VP_FATFS_VS_Generic
Mcu.Pin36=VP_FREERTOS_VS_CMSIS_V2
Mcu.Pin37=VP_IWDG_VS_IWDG
Mcu.Pin38=VP_SYS_VS_tim3
Mcu.Pin39=VP_USB_HOST_VS_USB_HOST_CDC_FS
Mcu.Pin4=PH1-OSC_OUT
Mcu.Pin40=VP_WWDG_VS_WWDG
Mcu.Pin5=PC0
Mcu.Pin6=PC3
Mcu.Pin7=PA0-WKUP
Mcu.Pin8=PA2
Mcu.Pin9=PA3
Mcu.PinsNb=41
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false\:false
NVIC.OTG_FS_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:true\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:true\:true\:false
NVIC.TIM3_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM3_IRQn
NVIC.TimeBaseIP=TIM3
NVIC.USART2_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false\:false
NVIC.WWDG_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
PA0-WKUP.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PA0-WKUP.GPIO_Label=B1 [Blue PushButton]
PA0-WKUP.GPIO_ModeDefaultEXTI=GPIO_MODE_EVT_RISING
PA0-WKUP.GPIO_PuPd=GPIO_NOPULL
PA0-WKUP.Locked=true
PA0-WKUP.Signal=GPXTI0
PA10.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA10.GPIO_Label=OTG_FS_ID
PA10.GPIO_Mode=GPIO_MODE_AF_PP
PA10.GPIO_PuPd=GPIO_NOPULL
PA10.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA10.Locked=true
PA10.Signal=USB_OTG_FS_ID
PA11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA11.GPIO_Label=OTG_FS_DM
PA11.GPIO_Mode=GPIO_MODE_AF_PP
PA11.GPIO_PuPd=GPIO_NOPULL
PA11.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA11.Locked=true
PA11.Mode=Host_Only
PA11.Signal=USB_OTG_FS_DM
PA12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA12.GPIO_Label=OTG_FS_DP
PA12.GPIO_Mode=GPIO_MODE_AF_PP
PA12.GPIO_PuPd=GPIO_NOPULL
PA12.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA12.Locked=true
PA12.Mode=Host_Only
PA12.Signal=USB_OTG_FS_DP
PA13.GPIOParameters=GPIO_Label
PA13.GPIO_Label=SWDIO
PA13.Locked=true
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.GPIOParameters=GPIO_Label
PA14.GPIO_Label=SWCLK
PA14.Locked=true
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA4.GPIO_Label=I2S3_WS [CS43L22_LRCK]
PA4.GPIO_Mode=GPIO_MODE_AF_PP
PA4.GPIO_PuPd=GPIO_NOPULL
PA4.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA4.Locked=true
PA4.Mode=Half_Duplex_Master
PA4.Signal=I2S3_WS
PA5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA5.GPIO_Label=SPI1_SCK [LIS302DL_SCL/SPC]
PA5.GPIO_Mode=GPIO_MODE_AF_PP
PA5.GPIO_PuPd=GPIO_NOPULL
PA5.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA5.Locked=true
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA6.GPIO_Label=SPI1_MISO [LIS302DL_SDO]
PA6.GPIO_Mode=GPIO_MODE_AF_PP
PA6.GPIO_PuPd=GPIO_NOPULL
PA6.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA6.Locked=true
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA7.GPIO_Label=SPI1_MOSI [LIS302DL_SDA/SDI/SDO]
PA7.GPIO_Mode=GPIO_MODE_AF_PP
PA7.GPIO_PuPd=GPIO_NOPULL
PA7.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA7.Locked=true
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA9.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PA9.GPIO_Label=VBUS_FS
PA9.GPIO_Mode=GPIO_MODE_INPUT
PA9.GPIO_PuPd=GPIO_NOPULL
PA9.Locked=true
PA9.Mode=Activate_VBUS
PA9.Signal=USB_OTG_FS_VBUS
PB10.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PB10.GPIO_Label=CLK_IN [MP45DT02_CLK]
PB10.GPIO_Mode=GPIO_MODE_AF_PP
PB10.GPIO_PuPd=GPIO_NOPULL
PB10.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB10.Locked=true
PB10.Signal=I2S2_CK
PB2.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PB2.GPIO_Label=BOOT1
PB2.GPIO_Mode=GPIO_MODE_INPUT
PB2.GPIO_PuPd=GPIO_NOPULL
PB2.Locked=true
PB2.Signal=GPIO_Input
PB3.GPIOParameters=GPIO_Label
PB3.GPIO_Label=SWO
PB3.Locked=true
PB3.Signal=SYS_JTDO-SWO
PB6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Pu,GPIO_Mode
PB6.GPIO_Label=Audio_SCL [CS43L22_SCL]
PB6.GPIO_Mode=GPIO_MODE_AF_OD
PB6.GPIO_Pu=GPIO_PULLUP
PB6.GPIO_PuPd=GPIO_NOPULL
PB6.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB6.Locked=true
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Pu,GPIO_Mode
PB9.GPIO_Label=Audio_SDA [CS43L22_SDA]
PB9.GPIO_Mode=GPIO_MODE_AF_OD
PB9.GPIO_Pu=GPIO_PULLUP
PB9.GPIO_PuPd=GPIO_NOPULL
PB9.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC0.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label
PC0.GPIO_Label=OTG_FS_PowerSwitchOn
PC0.GPIO_PuPd=GPIO_NOPULL
PC0.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC0.Locked=true
PC0.PinState=GPIO_PIN_SET
PC0.Signal=GPIO_Output
PC10.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PC10.GPIO_Label=I2S3_SCK [CS43L22_SCLK]
PC10.GPIO_Mode=GPIO_MODE_AF_PP
PC10.GPIO_PuPd=GPIO_NOPULL
PC10.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC10.Locked=true
PC10.Mode=Half_Duplex_Master
PC10.Signal=I2S3_CK
PC12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PC12.GPIO_Label=I2S3_SD [CS43L22_SDIN]
PC12.GPIO_Mode=GPIO_MODE_AF_PP
PC12.GPIO_PuPd=GPIO_NOPULL
PC12.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC12.Locked=true
PC12.Mode=Half_Duplex_Master
PC12.Signal=I2S3_SD
PC14-OSC32_IN.GPIOParameters=GPIO_Label
PC14-OSC32_IN.GPIO_Label=PC14-OSC32_IN
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.GPIOParameters=GPIO_Label
PC15-OSC32_OUT.GPIO_Label=PC15-OSC32_OUT
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC3.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PC3.GPIO_Label=PDM_OUT [MP45DT02_DOUT]
PC3.GPIO_Mode=GPIO_MODE_AF_PP
PC3.GPIO_PuPd=GPIO_NOPULL
PC3.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC3.Locked=true
PC3.Signal=I2S2_SD
PC7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PC7.GPIO_Label=I2S3_MCK [CS43L22_MCLK]
PC7.GPIO_Mode=GPIO_MODE_AF_PP
PC7.GPIO_PuPd=GPIO_NOPULL
PC7.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC7.Locked=true
PC7.Mode=Master_Clock_Activated
PC7.Signal=I2S3_MCK
PD12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD12.GPIO_Label=LD4 [Green Led]
PD12.GPIO_PuPd=GPIO_NOPULL
PD12.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD12.Locked=true
PD12.Signal=GPIO_Output
PD13.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD13.GPIO_Label=LD3 [Orange Led]
PD13.GPIO_PuPd=GPIO_NOPULL
PD13.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD13.Locked=true
PD13.Signal=GPIO_Output
PD14.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD14.GPIO_Label=LD5 [Red Led]
PD14.GPIO_PuPd=GPIO_NOPULL
PD14.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD14.Locked=true
PD14.Signal=GPIO_Output
PD15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD15.GPIO_Label=LD6 [Blue Led]
PD15.GPIO_PuPd=GPIO_NOPULL
PD15.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD15.Locked=true
PD15.Signal=GPIO_Output
PD4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD4.GPIO_Label=Audio_RST [CS43L22_RESET]
PD4.GPIO_PuPd=GPIO_NOPULL
PD4.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD4.Locked=true
PD4.Signal=GPIO_Output
PD5.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PD5.GPIO_Label=OTG_FS_OverCurrent
PD5.GPIO_Mode=GPIO_MODE_INPUT
PD5.GPIO_PuPd=GPIO_NOPULL
PD5.Locked=true
PD5.Signal=GPIO_Input
PE1.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PE1.GPIO_Label=MEMS_INT2 [LIS302DL_INT2]
PE1.GPIO_ModeDefaultEXTI=GPIO_MODE_EVT_RISING
PE1.GPIO_PuPd=GPIO_NOPULL
PE1.Locked=true
PE1.Signal=GPXTI1
PE3.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PE3.GPIO_Label=CS_I2C/SPI [LIS302DL_CS_I2C/SPI]
PE3.GPIO_PuPd=GPIO_NOPULL
PE3.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PE3.Locked=true
PE3.Signal=GPIO_Output
PH0-OSC_IN.GPIOParameters=GPIO_Label
PH0-OSC_IN.GPIO_Label=PH0-OSC_IN
PH0-OSC_IN.Locked=true
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.GPIOParameters=GPIO_Label
PH1-OSC_OUT.GPIO_Label=PH1-OSC_OUT
PH1-OSC_OUT.Locked=true
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=true
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=2
ProjectManager.MainLocation=Core/Src
ProjectManager.MultiThreaded=true
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=BaseFreeRTOSPort.ioc
ProjectManager.ProjectName=BaseFreeRTOSPort
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=CMake
ProjectManager.ThreadSafeStrategy=Cortex-M4NS\:Default,
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_I2C1_Init-I2C1-false-HAL-true,4-MX_I2S3_Init-I2S3-false-HAL-true,5-MX_SPI1_Init-SPI1-false-HAL-true,6-MX_USB_HOST_Init-USB_HOST-false-HAL-false,7-MX_USART2_UART_Init-USART2-false-HAL-true,8-MX_FATFS_Init-FATFS-false-HAL-false,9-MX_IWDG_Init-IWDG-false-HAL-true,10-MX_WWDG_Init-WWDG-false-HAL-true
RCC.48MHZClocksFreq_Value=48000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=336
RCC.PLLQ=7
RCC.PLLQCLKFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=192000000
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=96000000
SH.GPXTI0.0=GPIO_EXTI0
SH.GPXTI0.ConfNb=1
SH.GPXTI1.0=GPIO_EXTI1
SH.GPXTI1.ConfNb=1
SPI1.BaudRatePrescaler-Full_Duplex_Master=SPI_BAUDRATEPRESCALER_2
SPI1.CalculateBaudRate-Full_Duplex_Master=42.0 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=CalculateBaudRate-Full_Duplex_Master,BaudRatePrescaler-Full_Duplex_Master,Mode-Full_Duplex_Master,Mode,VirtualType,Direction
SPI1.Mode=SPI_MODE_MASTER
SPI1.Mode-Full_Duplex_Master=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USB_HOST.BSP.number=1
USB_HOST.IPParameters=VirtualModeFS,USBH_HandleTypeDef-CDC_FS
USB_HOST.USBH_HandleTypeDef-CDC_FS=hUsbHostFS
USB_HOST.VirtualModeFS=Cdc
USB_HOST0.BSP.STBoard=false
USB_HOST0.BSP.api=Unknown
USB_HOST0.BSP.component=
USB_HOST0.BSP.condition=
USB_HOST0.BSP.instance=PC0
USB_HOST0.BSP.ip=GPIO
USB_HOST0.BSP.mode=Output
USB_HOST0.BSP.name=Drive_VBUS_FS
USB_HOST0.BSP.semaphore=
USB_HOST0.BSP.solution=PC0
USB_OTG_FS.IPParameters=phy_itface,VirtualMode
USB_OTG_FS.VirtualMode=Host_Only
USB_OTG_FS.phy_itface=HCD_PHY_EMBEDDED
VP_FATFS_VS_Generic.Mode=User_defined
VP_FATFS_VS_Generic.Signal=FATFS_VS_Generic
VP_FREERTOS_VS_CMSIS_V2.Mode=CMSIS_V2
VP_FREERTOS_VS_CMSIS_V2.Signal=FREERTOS_VS_CMSIS_V2
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_SYS_VS_tim3.Mode=TIM3
VP_SYS_VS_tim3.Signal=SYS_VS_tim3
VP_USB_HOST_VS_USB_HOST_CDC_FS.Mode=CDC_FS
VP_USB_HOST_VS_USB_HOST_CDC_FS.Signal=USB_HOST_VS_USB_HOST_CDC_FS
VP_WWDG_VS_WWDG.Mode=WWDG_Activate
VP_WWDG_VS_WWDG.Signal=WWDG_VS_WWDG
board=STM32F407G-DISC1
boardIOC=true
