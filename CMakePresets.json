{"version": 3, "configurePresets": [{"name": "default", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "toolchainFile": "${sourceDir}/cmake/gcc-arm-none-eabi.cmake", "cacheVariables": {"CMAKE_EXPORT_COMPILE_COMMANDS": "ON"}}, {"name": "debug", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "relWithDebInfo", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "RelWithDebInfo"}}, {"name": "release", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"name": "minSizeRel", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "MinSizeRel"}}], "buildPresets": [{"name": "debug", "configurePreset": "debug"}, {"name": "relWithDebInfo", "configurePreset": "relWithDebInfo"}, {"name": "release", "configurePreset": "release"}, {"name": "minSizeRel", "configurePreset": "minSizeRel"}]}