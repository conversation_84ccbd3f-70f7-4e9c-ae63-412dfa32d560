# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: BaseFreeRTOSPort
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build/
# =============================================================================
# Object build statements for EXECUTABLE target BaseFreeRTOSPort


#############################################
# Order-only phony target for BaseFreeRTOSPort

build cmake_object_order_depends_target_BaseFreeRTOSPort: phony || cmake_object_order_depends_target_STM32_Drivers

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/main.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/main.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\main.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/gpio.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/gpio.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\gpio.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/i2c.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/i2c.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\i2c.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/i2s.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/i2s.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\i2s.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/spi.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/spi.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\spi.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/stm32f4xx_it.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/stm32f4xx_it.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\stm32f4xx_it.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/stm32f4xx_hal_msp.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/stm32f4xx_hal_msp.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\stm32f4xx_hal_msp.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/sysmem.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/sysmem.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\sysmem.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/syscalls.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/syscalls.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src\syscalls.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Core\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/startup_stm32f407xx.s.obj: ASM_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/startup_stm32f407xx.s || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\startup_stm32f407xx.s.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -x assembler-with-cpp -MMD -MP -g
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/tasks.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/tasks.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\tasks.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/queue.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/queue.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\queue.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/list.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/list.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\list.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/timers.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/timers.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\timers.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/event_groups.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/event_groups.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F\port.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM4F
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb

build CMakeFiles/BaseFreeRTOSPort.dir/Application/Src/app.c.obj: C_COMPILER__BaseFreeRTOSPort_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Src/app.c || cmake_object_order_depends_target_BaseFreeRTOSPort
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = CMakeFiles\BaseFreeRTOSPort.dir\Application\Src\app.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  OBJECT_FILE_DIR = CMakeFiles\BaseFreeRTOSPort.dir\Application\Src
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_PDB = BaseFreeRTOSPort.pdb


# =============================================================================
# Link build statements for EXECUTABLE target BaseFreeRTOSPort


#############################################
# Link the executable BaseFreeRTOSPort.elf

build BaseFreeRTOSPort.elf: C_EXECUTABLE_LINKER__BaseFreeRTOSPort_Debug cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f4xx.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2s.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2s_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/main.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/gpio.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/i2c.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/i2s.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/spi.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/stm32f4xx_it.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/stm32f4xx_hal_msp.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/sysmem.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Core/Src/syscalls.c.obj CMakeFiles/BaseFreeRTOSPort.dir/startup_stm32f407xx.s.obj CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/tasks.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/queue.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/list.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/timers.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/event_groups.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.c.obj CMakeFiles/BaseFreeRTOSPort.dir/Application/Src/app.c.obj || cmake/stm32cubemx/STM32_Drivers
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3
  LINK_FLAGS = -TC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/STM32F407XX_FLASH.ld -Wl,-Map=BaseFreeRTOSPort.map -Wl,--gc-sections -Wl,--start-group -lc -lm -Wl,--end-group -Wl,--print-memory-usage
  OBJECT_DIR = CMakeFiles\BaseFreeRTOSPort.dir
  POST_BUILD = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build && arm-none-eabi-objcopy -O binary BaseFreeRTOSPort BaseFreeRTOSPort.bin && arm-none-eabi-objcopy -O ihex BaseFreeRTOSPort BaseFreeRTOSPort.hex && arm-none-eabi-size BaseFreeRTOSPort"
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\BaseFreeRTOSPort.dir\
  TARGET_FILE = BaseFreeRTOSPort.elf
  TARGET_PDB = BaseFreeRTOSPort.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build && C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake-gui.exe -SC:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort -BC:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build && C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe --regenerate-during-build -SC:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort -BC:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target STM32_Drivers


#############################################
# Order-only phony target for STM32_Drivers

build cmake_object_order_depends_target_STM32_Drivers: phony || cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f4xx.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Src/system_stm32f4xx.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Core\Src\system_stm32f4xx.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Core\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2s.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2s.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2s.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2s_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2s_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2s_ex.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=2 -DUSE_HAL_DRIVER
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_spi.c.obj.d
  FLAGS = -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Device/ST/STM32F4xx/Include" -I"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/CMSIS/Include"
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\C_\Users\Krishna_Singh\STM32Cube\Repository\STM32Cube_FW_F4_V1.28.1\Drivers\STM32F4xx_HAL_Driver\Src
  TARGET_COMPILE_PDB = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\
  TARGET_PDB = ""



#############################################
# Object library STM32_Drivers

build cmake/stm32cubemx/STM32_Drivers: phony cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f4xx.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2s.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2s_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/C_/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.1/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c.obj


#############################################
# Utility command for edit_cache

build cmake/stm32cubemx/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build\cmake\stm32cubemx && C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake-gui.exe -SC:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort -BC:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build cmake/stm32cubemx/edit_cache: phony cmake/stm32cubemx/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build cmake/stm32cubemx/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build\cmake\stm32cubemx && C:\ST\STM32CubeCLT_1.18.0\CMake\bin\cmake.exe --regenerate-during-build -SC:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort -BC:\MyWorkSpaces\stm32_freertos_workspace\BaseFreeRTOSPort\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build cmake/stm32cubemx/rebuild_cache: phony cmake/stm32cubemx/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build BaseFreeRTOSPort: phony BaseFreeRTOSPort.elf

build STM32_Drivers: phony cmake/stm32cubemx/STM32_Drivers

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build

build all: phony BaseFreeRTOSPort.elf cmake/stm32cubemx/all

# =============================================================================

#############################################
# Folder: C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build/cmake/stm32cubemx

build cmake/stm32cubemx/all: phony cmake/stm32cubemx/STM32_Drivers

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/CMakeLists.txt C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/cmake/gcc-arm-none-eabi.cmake C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/cmake/stm32cubemx/CMakeLists.txt C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeASMCompiler.cmake.in C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeASMInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCCompilerABI.c C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineASMCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeNinjaFindMake.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystem.cmake.in C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestASMCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake CMakeCache.txt CMakeFiles/3.28.1/CMakeASMCompiler.cmake CMakeFiles/3.28.1/CMakeCCompiler.cmake CMakeFiles/3.28.1/CMakeCXXCompiler.cmake CMakeFiles/3.28.1/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/CMakeLists.txt C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/cmake/gcc-arm-none-eabi.cmake C$:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/cmake/stm32cubemx/CMakeLists.txt C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeASMCompiler.cmake.in C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeASMInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCCompilerABI.c C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineASMCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeNinjaFindMake.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystem.cmake.in C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestASMCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake C$:/ST/STM32CubeCLT_1.18.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake CMakeCache.txt CMakeFiles/3.28.1/CMakeASMCompiler.cmake CMakeFiles/3.28.1/CMakeCCompiler.cmake CMakeFiles/3.28.1/CMakeCXXCompiler.cmake CMakeFiles/3.28.1/CMakeSystem.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
