[{"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\USB_HOST\\Target\\usbh_conf.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\USB_HOST\\Target\\usbh_conf.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\USB_HOST\\Target\\usbh_conf.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\USB_HOST\\Target\\usbh_conf.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\USB_HOST\\Target\\usbh_platform.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\USB_HOST\\Target\\usbh_platform.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\USB_HOST\\Target\\usbh_platform.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\USB_HOST\\Target\\usbh_platform.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\USB_HOST\\App\\usb_host.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\USB_HOST\\App\\usb_host.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\USB_HOST\\App\\usb_host.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\USB_HOST\\App\\usb_host.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\FATFS\\App\\fatfs.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\FATFS\\App\\fatfs.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\FATFS\\App\\fatfs.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\FATFS\\App\\fatfs.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\FATFS\\Target\\user_diskio.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\FATFS\\Target\\user_diskio.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\FATFS\\Target\\user_diskio.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\FATFS\\Target\\user_diskio.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\main.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\main.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\main.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\main.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\gpio.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\gpio.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\gpio.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\gpio.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\freertos.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\freertos.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\freertos.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\freertos.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\i2c.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\i2c.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\i2c.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\i2c.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\i2s.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\i2s.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\i2s.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\i2s.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\iwdg.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\iwdg.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\iwdg.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\iwdg.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\spi.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\spi.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\spi.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\spi.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\usart.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\usart.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\usart.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\usart.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\wwdg.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\wwdg.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\wwdg.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\wwdg.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\stm32f4xx_it.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\stm32f4xx_it.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\stm32f4xx_it.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\stm32f4xx_it.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\stm32f4xx_hal_msp.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\stm32f4xx_hal_msp.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\stm32f4xx_hal_msp.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\stm32f4xx_hal_msp.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\stm32f4xx_hal_timebase_tim.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\stm32f4xx_hal_timebase_tim.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\stm32f4xx_hal_timebase_tim.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\stm32f4xx_hal_timebase_tim.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\sysmem.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\sysmem.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\sysmem.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\sysmem.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\syscalls.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\syscalls.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\syscalls.c", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\Core\\Src\\syscalls.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/include -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Application/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -x assembler-with-cpp -MMD -MP -g -o CMakeFiles\\BaseFreeRTOSPort.dir\\startup_stm32f407xx.s.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\startup_stm32f407xx.s", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\startup_stm32f407xx.s", "output": "CMakeFiles\\BaseFreeRTOSPort.dir\\startup_stm32f407xx.s.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Core\\Src\\system_stm32f4xx.c.obj -c C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\system_stm32f4xx.c", "file": "C:\\MyWorkSpaces\\stm32_freertos_workspace\\BaseFreeRTOSPort\\Core\\Src\\system_stm32f4xx.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Core\\Src\\system_stm32f4xx.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_hcd.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_hcd.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_hcd.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_hcd.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_usb.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_usb.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_usb.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_usb.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2s.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2s.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2s.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2s.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2s_ex.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2s_ex.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2s_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2s_ex.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_iwdg.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_iwdg.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_iwdg.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_iwdg.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_wwdg.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_wwdg.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_wwdg.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_wwdg.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\GCC\\ARM_CM4F\\port.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\GCC\\ARM_CM4F\\port.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\GCC\\ARM_CM4F\\port.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FreeRTOS.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\GCC\\ARM_CM4F\\port.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\diskio.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\diskio.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\diskio.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\diskio.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\ff.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\ff.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\ff.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\<PERSON><PERSON>Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\ff.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\ff_gen_drv.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\option\\syscall.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\option\\ccsbcs.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\option\\ccsbcs.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\option\\ccsbcs.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\FatFs.dir\\C_\\Users\\<PERSON><PERSON>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\Third_Party\\FatFs\\src\\option\\ccsbcs.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_core.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_core.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_core.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_core.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_ctlreq.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_ctlreq.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_ctlreq.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_ctlreq.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_ioreq.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_ioreq.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_ioreq.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_ioreq.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_pipes.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_pipes.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_pipes.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Core\\Src\\usbh_pipes.c.obj"}, {"directory": "C:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/build", "command": "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32F407xx -DSTM32_THREAD_SAFE_STRATEGY=4 -DUSE_HAL_DRIVER -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/Core/Inc -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/Target -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/FATFS/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/App -IC:/MyWorkSpaces/stm32_freertos_workspace/BaseFreeRTOSPort/USB_HOST/Target -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/STM32F4xx_HAL_Driver/Inc/Legacy\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/Third_Party/FatFs/src\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Core/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Middlewares/ST/STM32_USB_Host_Library/Class/CDC/Inc\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Device/ST/STM32F4xx/Include\" -I\"C:/Users/<USER>/STM32Cube/Repository/STM32Cube_FW_F4_V1.28.2/Drivers/CMSIS/Include\"  -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\Krishna_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Class\\CDC\\Src\\usbh_cdc.c.obj -c \"C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Class\\CDC\\Src\\usbh_cdc.c\"", "file": "C:\\Users\\<USER>\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Class\\CDC\\Src\\usbh_cdc.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\USB_Host_Library.dir\\C_\\Users\\<PERSON>_Singh\\STM32Cube\\Repository\\STM32Cube_FW_F4_V1.28.2\\Middlewares\\ST\\STM32_USB_Host_Library\\Class\\CDC\\Src\\usbh_cdc.c.obj"}]