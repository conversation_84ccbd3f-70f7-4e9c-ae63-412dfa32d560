# Create directories
New-Item -ItemType Directory -Force -Path "Middlewares/Third_Party/FreeRTOS/Source/include"
New-Item -ItemType Directory -Force -Path "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F"
New-Item -ItemType Directory -Force -Path "Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang"

# Download FreeRTOS source files
$baseUrl = "https://raw.githubusercontent.com/FreeRTOS/FreeRTOS-Kernel/main"
$files = @(
    "/tasks.c",
    "/queue.c",
    "/list.c",
    "/timers.c",
    "/event_groups.c",
    "/include/task.h",
    "/include/queue.h",
    "/include/list.h",
    "/include/timers.h",
    "/include/event_groups.h",
    "/include/FreeRTOS.h",
    "/include/semphr.h",
    "/include/portable.h",
    "/include/projdefs.h",
    "/include/portmacro.h",
    "/include/mpu_wrappers.h",
    "/include/StackMacros.h",
    "/include/croutine.h",
    "/include/deprecated_definitions.h",
    "/portable/GCC/ARM_CM4F/port.c",
    "/portable/GCC/ARM_CM4F/portmacro.h",
    "/portable/MemMang/heap_4.c"
)

foreach ($file in $files) {
    $targetPath = "Middlewares/Third_Party/FreeRTOS/Source$file"
    $url = "$baseUrl$file"
    Write-Host "Downloading $url to $targetPath"
    Invoke-WebRequest -Uri $url -OutFile $targetPath
} 